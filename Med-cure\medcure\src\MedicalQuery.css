.medical-query-wrapper {
  max-width: 650px;
  margin: auto;
  padding: 24px;
  font-family: 'Segoe UI', sans-serif;
  color: #333;
}

.method-toggle {
  margin-bottom: 16px;
}

.method-toggle label {
  margin-right: 16px;
  font-weight: 500;
}

.selected {
  color: #007cf0;
}

.text-box {
  width: 100%;
  height: 120px;
  margin: 12px 0;
  padding: 12px;
  font-size: 16px;
  border-radius: 8px;
  border: 1px solid #ccc;
}

.start-btn {
  padding: 10px 18px;
  background-color: #007cf0;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  cursor: pointer;
}

.recording-text {
  color: red;
  margin-top: 8px;
}

.upload-section {
  margin: 24px 0;
}

.folder-icon {
  font-size: 32px;
}

.file-upload {
  margin-top: 10px;
}

.analyze-btn {
  margin-top: 12px;
  padding: 10px 20px;
  background: linear-gradient(to right, #007cf0, #00dfd8);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  cursor: pointer;
}

.error-message {
  color: red;
  margin-top: 10px;
  font-weight: bold;
}

.response-box {
  margin-top: 20px;
  padding: 12px;
  background: #f0f8ff;
  border: 1px solid #007cf0;
  border-radius: 8px;
}

