import React from 'react';
import './WelcomeCard.css';

const WelcomeCard = ({ name }) => {
  const initials = name
    .split(' ')
    .map(part => part[0])
    .join('')
    .toUpperCase();

  return (
    <div className="welcome-card">
      <div className="welcome-left">
        <div className="user-avatar">{initials}</div>
        <div className="welcome-text">
          <h2>Welcome back, <span className="highlight-name">{name}!</span></h2>
          <p>How can we assist you with your medical inquiries today?</p>
        </div>
      </div>
      <div className="welcome-right">
        👋 Logged in as {initials}
      </div>
    </div>
  );
};

export default WelcomeCard;
