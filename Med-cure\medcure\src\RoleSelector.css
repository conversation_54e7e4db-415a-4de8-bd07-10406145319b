.role-selector-wrapper {
  font-family: 'Segoe UI', sans-serif;
  text-align: center;
  padding: 2rem;
}

.cards-container {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin: 2rem 0;
  flex-wrap: wrap;
}

.card {
  width: 260px;
  padding: 1.5rem;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s, box-shadow 0.3s;
  cursor: pointer;
  background-color: #fefefe;
}

.card:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.card.active {
  border: 2px solid #007cf0;
  background-color: #e6f7ff;
}

.emoji {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.select-btn {
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  background-color: #007cf0;
  color: white;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  font-weight: bold;
}

.select-btn:hover {
  background-color: #005fa3;
}

.current-role {
  margin-top: 1rem;
  font-size: 1.2rem;
}
