.settings-container {
  padding: 2rem;
  max-width: 800px;
  margin: auto;
  font-family: 'Segoe UI', sans-serif;
}

.tabs {
  display: flex;
  gap: 1rem;
  border-bottom: 2px solid #eee;
  margin-bottom: 1rem;
}

.tab {
  background: none;
  border: none;
  font-size: 1rem;
  padding: 0.5rem 0;
  cursor: pointer;
  border-bottom: 3px solid transparent;
  transition: 0.2s;
}

.tab.active {
  color: #e63946;
  border-color: #e63946;
  font-weight: bold;
}

.tab-content h2 {
  margin-top: 1.5rem;
  font-size: 1.4rem;
}

.profile-card {
  background: white;
  padding: 1rem;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  margin-bottom: 2rem;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

input {
  padding: 0.75rem;
  border-radius: 8px;
  border: none;
  background: #f1f3f8;
  font-size: 1rem;
}

.update-button {
  padding: 0.75rem;
  border: none;
  border-radius: 999px;
  background: #007bff;
  color: white;
  font-weight: bold;
  cursor: pointer;
  width: fit-content;
}
.password-input {
  position: relative;
  display: flex;
  align-items: center;
}

.password-input input {
  width: 100%;
  padding-right: 40px;
}

.eye-icon {
  position: absolute;
  right: 10px;
  cursor: pointer;
  user-select: none;
  font-size: 16px;
}

input.error {
  border: 1px solid red;
}
.checkbox-group label {
  display: block;
  margin-bottom: 10px;
  font-weight: 500;
}

.role-select {
  width: 100%;
  padding: 12px;
  border-radius: 8px;
  border: none;
  background-color: #f3f5f9;
  font-size: 16px;
}

.preferences-form h2 {
  margin-top: 30px;
}
