import React from 'react';
import './MedcodeBanner.css';
import { useLocation } from 'react-router-dom';

const MedcodeBanner = () => {
  const location = useLocation();

  // Map routes to banner content
  const bannerContent = {
    '/': {
      title: 'MedCode AI 🩺',
      subtitle: 'Your AI-powered medical assistant for personalized healthcare insights',
    },
    '/query-history': {
      title: 'Query History 📜',
      subtitle: 'Explore your previous health queries and insights',
    },
    '/settings': {
      title: 'Settings ⚙️',
      subtitle: 'Manage your profile and preferences',
    },
  };

  const { title, subtitle } = bannerContent[location.pathname] || bannerContent['/'];

  return (
    <div className="banner-wrapper">
      <div className="banner">
        <h1 className="banner-title">{title}</h1>
        <p className="banner-subtitle">{subtitle}</p>
      </div>
    </div>
  );
};

export default MedcodeBanner;
