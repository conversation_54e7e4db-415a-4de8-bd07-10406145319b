import './LeftScreen.css'
import React, { useState } from 'react';
import Avatar from '@mui/material/Avatar';
import MenuIcon from '@mui/icons-material/Menu';
import { deepOrange } from '@mui/material/colors';
import { NavLink } from 'react-router-dom';

function LeftScreen () {
  
  const navItems = [
        { label: 'Dashboard', path: '/' },
        { label: 'Query History', path: '/query-history' },
        { label: 'Settings', path: '/settings' }
    ];

  return (
    <div className='LeftScreen'>
      <div className='header'>
        <img src="/medical-sign.jpg" alt="Medical Sign" />
        <p>MedCode AI</p>
      </div>


      <div className="centerContainer">
        <div className="userProfile">
            <Avatar sx={{ bgcolor: deepOrange[500], width: 60, height: 60 }}>H</Avatar>
            <div className="userDetails">
            <h3>Username</h3>
            <p>User Category</p>
            </div>
        </div>
      </div>
    
      <hr className="horizontal-divider" />



      <div className="nav-container">
        <h4 className="nav-heading">Navigation</h4>
        <ul className="nav-list">
            {navItems.map(({ label, path }) => (
                <li key={label}>
                <NavLink
                    to={path}
                    className={({ isActive }) =>
                    `nav-item ${isActive ? 'active' : ''}`
                    }
                >
                    {label}
                </NavLink>
                </li>
            ))}
        </ul>
      </div>

      <hr className="horizontal-divider" />



      <div className='bottom-body'>
        <h4 className="nav-heading">Your Stats</h4>
        <div className='body-items'>
          <span>
            <p>Queries</p>
            <h2>0</h2>
          </span>
          <span>
            <p>Days Active</p>
            <h2>2</h2>
          </span>
        </div>
      </div>
      
      <hr className="horizontal-divider" />


      <div className='logout_button'>LOGOUT</div>

      <div className='bottom'>
        <p>©2025 MedCode AI | MIT License</p>
      </div>
      
    </div>
  )
}

export default LeftScreen
