body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}


::-webkit-scrollbar {
  width: 14px;
}

::-webkit-scrollbar-track {
  background: #e0e0e0;
  border-radius: 8px;
}

::-webkit-scrollbar-thumb {
  background-color: #007bff;
  border-radius: 8px;
  border: 2px solid #e0e0e0;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #007bff;
}

* {
  scrollbar-width: thin;
  scrollbar-color: #007bff #e0e0e0;
}

