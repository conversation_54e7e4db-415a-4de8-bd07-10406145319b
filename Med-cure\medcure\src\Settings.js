import React, { useState } from 'react';
import './Settings.css';
import Footer from './Footer';

const Settings = () => {
  const [activeTab, setActiveTab] = useState('profile');

  const [fullName, setFullName] = useState('Harshil HK');
  const [email, setEmail] = useState('<EMAIL>');
  const [profileInfo, setProfileInfo] = useState({
    username: '<PERSON><PERSON><PERSON>',
    name: '<PERSON><PERSON><PERSON> <PERSON>',
    role: 'Doctor',
  });

  const [selectedRole, setSelectedRole] = useState(profileInfo.role);

  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false,
  });

  const handleUpdateProfile = () => {
    setProfileInfo(prev => ({
      ...prev,
      name: fullName,
    }));
  };

  const handleSavePreferences = () => {
    setProfileInfo(prev => ({
      ...prev,
      role: selectedRole,
    }));
    alert(`Preferences saved! Role set to "${selectedRole}"`);
  };

  const togglePassword = (field) => {
    setShowPasswords(prev => ({
      ...prev,
      [field]: !prev[field],
    }));
  };

  const handleChangePassword = () => {
    if (!currentPassword || !newPassword || !confirmPassword) {
      alert("All fields are required.");
      return;
    }
    if (newPassword !== confirmPassword) {
      alert("New passwords do not match.");
      return;
    }
    alert("Password changed successfully.");
    setCurrentPassword('');
    setNewPassword('');
    setConfirmPassword('');
  };

  return (
    <div className="settings-container">
      <div className="tabs">
        <button className={activeTab === 'profile' ? 'tab active' : 'tab'} onClick={() => setActiveTab('profile')}>
          Profile
        </button>
        <button className={activeTab === 'preferences' ? 'tab active' : 'tab'} onClick={() => setActiveTab('preferences')}>
          Preferences
        </button>
      </div>

      {activeTab === 'profile' ? (
        <div className="tab-content">
          <h2>Profile Information</h2>
          <div className="profile-card">
            <p><strong>Username:</strong> {profileInfo.username}</p>
            <p><strong>Name:</strong> {profileInfo.name}</p>
            <p><strong>Role:</strong> {profileInfo.role}</p>
          </div>

          <h2>Update Profile</h2>
          <div className="form">
            <label>Full Name</label>
            <input type="text" value={fullName} onChange={(e) => setFullName(e.target.value)} />
            <label>Email Address</label>
            <input type="email" value={email} onChange={(e) => setEmail(e.target.value)} />
            <button className="update-button" onClick={handleUpdateProfile}>
              UPDATE PROFILE
            </button>
          </div>

          <h2>Security</h2>
          <div className="form">
            <label>Current Password</label>
            <div className="password-input">
              <input
                type={showPasswords.current ? 'text' : 'password'}
                value={currentPassword}
                onChange={(e) => setCurrentPassword(e.target.value)}
                className={currentPassword === '' ? 'error' : ''}
              />
              <span onClick={() => togglePassword('current')} className="eye-icon">👁️</span>
            </div>

            <label>New Password</label>
            <div className="password-input">
              <input
                type={showPasswords.new ? 'text' : 'password'}
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
              />
              <span onClick={() => togglePassword('new')} className="eye-icon">👁️</span>
            </div>

            <label>Confirm New Password</label>
            <div className="password-input">
              <input
                type={showPasswords.confirm ? 'text' : 'password'}
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
              />
              <span onClick={() => togglePassword('confirm')} className="eye-icon">👁️</span>
            </div>

            <button className="update-button" onClick={handleChangePassword}>
              CHANGE PASSWORD
            </button>
          </div>
        </div>
      ) : (
        <div className="tab-content">
          <h2>Preferences</h2>

          <h3>Notifications 🔔</h3>
          <div className="checkbox-group">
            <label>
              <input type="checkbox" defaultChecked /> Email Notifications
            </label>
            <label>
              <input type="checkbox" defaultChecked /> Application Notifications
            </label>
          </div>

          <h3 style={{ marginTop: '20px' }}>Default Role</h3>
          <p>Set your default role when using the application</p>
          <select
            className="role-select"
            value={selectedRole}
            onChange={(e) => setSelectedRole(e.target.value)}
          >
            <option value="Common User">Common User</option>
            <option value="Doctor">Doctor</option>
            <option value="Student">Student</option>
          </select>

          <button
            className="update-button"
            style={{ marginTop: '20px' }}
            onClick={handleSavePreferences}
          >
            SAVE PREFERENCES
          </button>
        </div>
      )}

      <Footer />
    </div>
  );
};

export default Settings;
