import React from 'react'
import './ActivityDashboard.css';

const ActivityCard = ({ title, value, subtitle, icon }) => {
  return (
    <div className="activity-card">
      <div className="card-title">{title}</div>
      <div className="card-value">{value}</div>
      {subtitle && <div className="card-subtitle">{subtitle}</div>}
      {icon && <div className="card-icon">{icon}</div>}
    </div>
  );
};

function ActivityDashboard () {
  return (
    <div className="dashboard">
      <h2 className="dashboard-heading">Your Activity</h2>
      <div className="card-container">
        <ActivityCard
          title="Queries Today"
          value="3"
          subtitle="↑ 20% from yesterday"
        />
        <ActivityCard
          title="Total Queries"
          value="12"
          subtitle="Medical Analysis"
        />
        <ActivityCard
          title="Current Role"
          value=""
          subtitle="Doctor"
          icon="🩺"
        />
      </div>
    </div>
  )
}

export default ActivityDashboard