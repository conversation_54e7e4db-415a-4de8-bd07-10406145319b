import React, { useEffect, useState } from 'react';
import Footer from './Footer';
import './QueryHistory.css';

const QueryHistory = () => {
  const [queries, setQueries] = useState([]);

  useEffect(() => {
    const savedQueries = JSON.parse(localStorage.getItem('queryHistory') || '[]');
    setQueries(savedQueries);
  }, []);

  return (
    <div className="query-history-container">
      {queries.length === 0 ? (
        <div className="empty-message">
          You haven't made any queries yet. <a href="/dashboard">Go to the Dashboard to start!</a>
        </div>
      ) : (
        queries.map((q, index) => (
          <div key={index} className="query-card">
            <div className="query-header">Query: {q.query}</div>
            <div className="query-body">
              <p><strong>Query:</strong> {q.query}</p>
              <p><strong>Role:</strong> {q.role}</p>
              <p><strong>Time:</strong> {q.time}</p>
              <button
                className="rerun-btn"
                onClick={() => {
                  localStorage.setItem('rerunQuery', JSON.stringify(q));
                  window.location.href = '/dashboard'; // redirect to MedicalQuery
                }}
              >
                RERUN THIS QUERY
              </button>

            </div>
          </div>
        ))
      )}
      <Footer />
    </div>
  );
};

export default QueryHistory;
