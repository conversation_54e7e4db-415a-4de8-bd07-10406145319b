.LeftScreen {
    background-color: #f4f4f4;
    width: 100%;
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.LeftScreen .header {
    flex-wrap: wrap;
    margin-left: 10%;
    margin-top: 25%;
    text-align: center;
}
.header img {
    background-color: #f4f4f4;
    max-width: 12vw;
    max-height: 12vh;
}
.header p {
    font-weight: 500;
    font-size: 20px;
}

.centerContainer {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    padding: 10px 0;
    margin-bottom: 6vh;
}
.userProfile {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
    padding: 16px;
    width: 15vw;
    gap: 20%;
}
.userDetails h3 {
    margin: 0;
    font-size: 18px;
}
.userDetails p {
    margin: 4px 0 0;
    font-size: 14px;
    color: gray;
}

/* --- DIVIDER --- */
.horizontal-divider {
    border: none;
    border-top: 1px solid #ccc;
    width: 90%;
    margin: 20px auto;
}

.nav-container {
    width: 200px;
    margin-left: 10%;
}
.nav-heading {
    margin-bottom: 5vh;
    font-size: 18px;
    font-weight: 600;
}
.nav-list {
    list-style: none;
    padding: 0;
    margin: 0;
}
.nav-item {
    text-decoration: none;
    position: relative;
    padding: 10px 12px;
    border-radius: 6px;
    cursor: pointer;
    color: #333;
    transition: background 0.2s, color 0.2s;
    display: inline-block;
    width: 80%;
}

.nav-item::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    height: 2px;
    width: 0%;
    background-color: #007bff;
    transition: width 0.3s ease, left 0.3s ease;
    transform: translateX(-50%);
}
.nav-item:hover::after {
    width: 96%;
    left: 50%;
}
.nav-item:hover {
    background-color: #eee;
    width: 85%; 
}
.nav-item.active {
    background-color: #ccc;
    width: 85%; 
}


.bottom-body {
    width: 200px;
    margin-left: 10%;
}
.bottom-body > .nav-heading {
    margin-bottom: 1vh;
}
.bottom-body > .body-items {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}

.logout_button {
    margin-top: 5vh;
    margin-left: 10%;
    background: linear-gradient(to right, #0052cc, #66b2ff);
    color: white;
    border: none;
    width: 120px;
    padding: 10px 20px;
    border-radius: 999px;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.3s ease, transform 0.2s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
}
.logout_button:hover {
    transform: scale(1.03);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

/* --- FOOTER --- */
.bottom {
    margin-left: 2vw;
    margin-top: 15px;
    color: #0000008f;
    font-size: 12px;
    text-align: center;
}


@media (max-width: 1024px) {
  .centerContainer {
  width: 100%;               
  display: flex;
  justify-content: center;   
  padding: 1rem 0;
}

.centerContainer .userProfile {
  width: 70%;               
  flex-direction: column;
  gap: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

  .nav-container,
  .bottom-body,
  .logout_button {
    width: 90%; 
    margin-left: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .nav-item,
  .nav-item.active,
  .nav-item:hover {
    width: 100%;
  }

  .bottom-body .body-items {
    width: 100%;
    display: flex;
    justify-content: space-around;
    gap: 2rem;
  }

  .logout_button {
    width: 80%;
  }

  .header img {
    max-width: 40%;
  }

  .header {
    margin-left: 0;
    text-align: center;
  }
}

@media (max-width: 600px) {
  .LeftScreen {
    width: 100%;
  }
    .header img {
        max-width: 30vw;
    }
    .centerContainer{
      width: 100vw;
    }
    .centerContainer .userProfile {
        width: 70%;
        flex-direction: column;
        gap: 10px;
    }

    .bottom-body,
    .logout_button {
        width: 70%;
    }

    .bottom-body .body-items{
      gap: 2rem;
    }
    .nav-item {
        font-size: 16px;
    }
}
