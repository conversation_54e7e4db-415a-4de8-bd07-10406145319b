import React, { useState, useEffect, useCallback } from 'react';
import './MedicalQuery.css';

const MedicalQuery = () => {
  const [inputMethod, setInputMethod] = useState('Text');
  const [isRecording, setIsRecording] = useState(false);
  const [query, setQuery] = useState('');
  const [imageFile, setImageFile] = useState(null);
  const [response, setResponse] = useState('');
  const [error, setError] = useState('');

  const handleSubmit = useCallback(async () => {
    if (!query && !imageFile) {
      setError('Please provide a question or upload a medical image.');
      return;
    }
    setError('');
    setResponse('');

    const formData = new FormData();
    formData.append('query', query);
    if (imageFile) formData.append('image', imageFile);

    const existingQueries = JSON.parse(localStorage.getItem('queryHistory') || '[]');
    const newQuery = {
      query: query || '[Image Only]',
      role: 'Doctor',
      time: new Date().toISOString().slice(0, 19).replace('T', ' ')
    };
    localStorage.setItem('queryHistory', JSON.stringify([newQuery, ...existingQueries]));

    try {
      setTimeout(() => {
        setResponse('🧠 Medical analysis complete! Your question was: "' + query + '".');
      }, 1500);
    } catch (err) {
      setError('Failed to submit. Please try again.');
    }
  }, [query, imageFile]);

  useEffect(() => {
    const rerun = localStorage.getItem('rerunQuery');
    if (rerun) {
      const parsed = JSON.parse(rerun);
      setQuery(parsed.query);
      setTimeout(() => {
        handleSubmit();
        localStorage.removeItem('rerunQuery');
      }, 500);
    }
  }, [handleSubmit]);

  const handleStartRecording = () => {
    setIsRecording(true);
    if ('webkitSpeechRecognition' in window) {
      const recognition = new window.webkitSpeechRecognition();
      recognition.lang = 'en-US';
      recognition.onresult = (event) => {
        const transcript = event.results[0][0].transcript;
        setQuery(transcript);
        setIsRecording(false);
      };
      recognition.onerror = () => {
        alert('Voice recognition error.');
        setIsRecording(false);
      };
      recognition.start();
    } else {
      alert('Voice recognition not supported in this browser.');
      setIsRecording(false);
    }
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file && ['image/png', 'image/jpeg', 'image/jpg'].includes(file.type)) {
      setImageFile(file);
      setError('');
    } else {
      setImageFile(null);
      setError('Only PNG, JPG, and JPEG files are allowed.');
    }
  };

  return (
    <div className="medical-query-wrapper">
      <h2>What would you like to know?</h2>
      <p><strong>Input Method:</strong></p>

      <div className="method-toggle">
        <label className={inputMethod === 'Text' ? 'selected' : ''}>
          <input
            type="radio"
            name="method"
            value="Text"
            checked={inputMethod === 'Text'}
            onChange={() => setInputMethod('Text')}
          /> Text
        </label>
        <label className={inputMethod === 'Voice' ? 'selected' : ''}>
          <input
            type="radio"
            name="method"
            value="Voice"
            checked={inputMethod === 'Voice'}
            onChange={() => setInputMethod('Voice')}
          /> Voice
        </label>
      </div>

      {inputMethod === 'Text' ? (
        <textarea
          className="text-box"
          placeholder="Enter your medical query here..."
          value={query}
          onChange={(e) => setQuery(e.target.value)}
        />
      ) : (
        <div className="voice-box">
          <button onClick={handleStartRecording} className="start-btn">🎤 Start Recording</button>
          {isRecording && <p className="recording-text">🎙️ Listening...</p>}
        </div>
      )}

      <div className="upload-section">
        <div className="folder-icon">📁</div>
        <h3>Upload Medical Image</h3>
        <p>Upload X-rays, lab results, medical scans or any relevant images</p>
        <input type="file" accept=".png,.jpg,.jpeg" className="file-upload" onChange={handleFileChange} />
        {imageFile && <p><strong>Selected:</strong> {imageFile.name}</p>}
      </div>

      {error && <p className="error-message">{error}</p>}

      <button className="analyze-btn" onClick={handleSubmit}>
        🔍 Generate Medical Analysis
      </button>

      {response && <div className="response-box">{response}</div>}
    </div>
  );
};

export default MedicalQuery;
