import React, { useState } from 'react';
import './RoleSelector.css';

const roles = [
  {
    name: 'Student',
    emoji: '🎓',
    description: 'Explore medical knowledge through research and case studies',
  },
  {
    name: 'Doctor',
    emoji: '👨‍⚕️',
    description: 'Analyze patient data and treatment methodologies',
  },
  {
    name: 'Common User',
    emoji: '🧑‍💼',
    description: 'Understand medical conditions in simple terms',
  },
];

const RoleSelector = () => {
  const [currentRole, setCurrentRole] = useState('Doctor');

  return (
    <div className="role-selector-wrapper">
      <h2>Select Your Role for this Query</h2>
      <div className="cards-container">
        {roles.map((role) => (
          <div
            key={role.name}
            className={`card ${currentRole === role.name ? 'active' : ''}`}
            onClick={() => setCurrentRole(role.name)}
          >
            <div className="emoji">{role.emoji}</div>
            <h3>{role.name}</h3>
            <p>{role.description}</p>
            <button className="select-btn">SELECT {role.name.toUpperCase()}</button>
          </div>
        ))}
      </div>
      <div className="current-role">
        <strong>Current Role:</strong> {roles.find(r => r.name === currentRole).emoji} {currentRole}
      </div>
    </div>
  );
};

export default RoleSelector;
