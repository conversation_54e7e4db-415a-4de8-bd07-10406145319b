.dashboard {
  padding: 40px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  width: 100%;
  box-sizing: border-box;
}

.dashboard-heading {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  text-align: center;
}

.card-container {
  display: flex;
  gap: 24px;
  flex-wrap: wrap;
  justify-content: center;
}

.activity-card {
  background-color: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 24px;
  width: 200px;
  transition: transform 0.3s ease;
}

.activity-card:hover {
  transform: scale(1.05);
}

.card-title {
  color: #6b7280;
  font-size: 14px;
  margin-bottom: 8px;
}

.card-value {
  font-size: 28px;
  font-weight: bold;
  color: #2563eb;
  margin-bottom: 4px;
}

.card-subtitle {
  font-size: 14px;
  color: #059669;
}

.card-icon {
  font-size: 32px;
  margin-top: 16px;
  color: #7c3aed;
}
@media (max-width: 600px) {
  .card-container {
    justify-content: center;
  }
}

.activity-card {
  background-color: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 24px;
  width: 200px;
  transition: transform 0.3s ease;
}

.activity-card:hover {
  transform: scale(1.05);
}

.card-title {
  color: #6b7280;
  font-size: 14px;
  margin-bottom: 8px;
}

.card-value {
  font-size: 28px;
  font-weight: bold;
  color: #2563eb;
  margin-bottom: 4px;
}

.card-subtitle {
  font-size: 14px;
  color: #059669;
}

.card-icon {
  font-size: 32px;
  margin-top: 16px;
  color: #7c3aed;
}