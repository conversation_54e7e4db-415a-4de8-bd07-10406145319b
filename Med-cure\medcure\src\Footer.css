.footer {
  width: 100vw; 
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
  margin-top: 5vh;
  text-align: center;
  padding: 16px 20px;
  background-color: #f9f9fb;
  border-top: 1px solid #eee;
  box-sizing: border-box;
}

.footer-links {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
  max-width: 900px;
  margin: 0 auto;
}

/* Add a class for separators */
.footer-links .separator {
  color: #888;
  user-select: none;
}

.footer-link {
  background: none;
  border: none;
  padding: 0;
  color: #007cf0;
  text-decoration: underline;
  cursor: pointer;
  font: inherit;
  white-space: nowrap;
}
@media (max-width: 600px) {
  .footer {
    padding: 12px 10px;
    font-size: 14px;
  }
  .footer-links {
    flex-direction: column;
    gap: 8px;
    align-items: center;
  }
  .footer-link {
    font-size: 16px;
  }

  .footer-links .separator {
    display: none;
  }
}