.App {
  display: flex;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.App .left-side {
  width: 20vw;
  transition: all 0.3s ease;
  background-color: #f4f4f4;
  height: 100%;
  overflow-y: auto;
  box-shadow: 2px 0 5px rgba(0,0,0,0.1);
  position: relative; 
  left: 0;
  z-index: 1;
}

.App .right-side {
  width: 80vw;
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  padding: 20px;
  flex: 1;
  transition: margin-left 0.3s ease, width 0.3s ease;
  box-sizing: border-box;
}

.hamburger {
  display: none;
}

@media (max-width: 1024px) {
  .App {
    flex-direction: column;
  }

  .App .left-side {
    position: fixed;
    left: -40vw;             
    top: 0;
    height: 100vh;
    width: 40vw;             
    z-index: 999;
    background: #f4f4f4;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.3);
    transition: left 0.3s ease;
  }

  .App .left-side.open {
    left: 0;
  }

  .App .right-side {
    width: 100vw;
    height: 100vh;
    padding: 20px;
    transition: filter 0.3s ease;
  }

  .App .right-side.blurred {
    filter: blur(1px);
    pointer-events: none;
  }

  .hamburger {
    display: block;
    position: fixed;
    top: 20px;
    left: 20px;
    background: none;
    border: none;
    cursor: pointer;
    z-index: 1000;
  }

  .hamburger span {
    display: block;
    width: 25px;
    height: 3px;
    background: #333;
    margin: 5px 0;
    transition: 0.4s;
  }
}

@media (max-width: 600px) {
  .App .left-side {
    width: 100vw;            
    left: -100vw;             
  }

  .App .left-side.open {
    left: 0;
  }
}